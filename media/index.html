<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width">
<style>
html, body {
	margin: 0;
	padding: 0;
	height: 100%;
	font-family: 'Arial', sans-serif;
}
#video {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgb(30, 30, 30);
}
#message {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	text-align: center;
	justify-content: center;
	font-size: 16px;
	font-weight: bold;
	color: white;
	pointer-events: none;
	padding: 20px;
	box-sizing: border-box;
	text-shadow: 0 0 5px black;
	flex-direction: column;
	gap: 20px;
}

#reconnect-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
	border-radius: 12px;
	color: white;
	padding: 6px 12px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
	pointer-events: auto;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	position: relative;
	overflow: hidden;
}

#reconnect-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
	background: linear-gradient(135deg, #7c8ff0 0%, #8a5cb8 100%);
}

#reconnect-btn:active {
	transform: translateY(0);
	box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

#reconnect-btn:disabled {
	background: #555;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

#reconnect-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
	transition: left 0.5s;
}

#reconnect-btn:hover::before {
	left: 100%;
}
</style>
<script defer src="./reader.js"></script>
</head>
<body>

<video id="video"></video>
<div id="message">
  <div id="message-text"></div>
  <button id="reconnect-btn" style="display: none;">重试</button>
</div>

<script>

// DOM 元素引用
const video = document.getElementById('video');
const message = document.getElementById('message');
const messageText = document.getElementById('message-text');
const reconnectBtn = document.getElementById('reconnect-btn');

// 全局配置
const MAX_ERRORS = 3; // 最大连续错误次数
let playUrl;
let defaultControls = false;

/**
 * 按钮控制器 - 管理按钮状态和行为
 */
class ButtonController {
  constructor(buttonElement) {
    this.button = buttonElement;
    this.currentHandler = null;
    this.mode = 'retry'; // 'retry' | 'connect'
  }

  // 设置按钮为连接模式
  setConnectMode(handler) {
    this.mode = 'connect';
    this.updateButton('开始连接', handler, false);
  }

  // 设置按钮为重试模式
  setRetryMode(handler) {
    this.mode = 'retry';
    this.updateButton('重试', handler, false);
  }

  // 设置按钮为加载状态
  setLoadingState(text = '连接中...') {
    this.button.disabled = true;
    this.button.innerText = text;
  }

  // 重置按钮状态
  resetState() {
    this.button.disabled = false;
    this.button.innerText = this.mode === 'connect' ? '开始连接' : '重试';
  }

  // 显示/隐藏按钮
  show() {
    this.button.style.display = 'block';
  }

  hide() {
    this.button.style.display = 'none';
  }

  // 更新按钮状态和事件处理器
  updateButton(text, handler, disabled = false) {
    // 移除旧的事件监听器
    if (this.currentHandler) {
      this.button.removeEventListener('click', this.currentHandler);
    }

    // 设置新的状态和处理器
    this.button.innerText = text;
    this.button.disabled = disabled;
    this.currentHandler = handler;

    if (handler) {
      this.button.addEventListener('click', handler);
    }
  }
}

/**
 * 连接管理器 - 管理WebRTC连接逻辑
 */
class ConnectionManager {
  constructor() {
    this.currentReader = null;
    this.errorCount = 0;
    this.currentConfig = null;
  }

  // 初始化连接
  async initializeConnection(config) {
    this.currentConfig = config;
    this.errorCount = 0;

    // 销毁之前的实例
    this.destroyCurrentReader();

    // 创建新的 Reader 实例
    this.currentReader = new MediaMTXWebRTCReader({
      url: config.url,
      headers: config.headers,
      onError: (err) => this.handleConnectionError(err),
      onTrack: (evt) => this.handleConnectionSuccess(evt),
    });
  }

  // 处理连接错误
  handleConnectionError(err) {
    console.error('MediaMTXWebRTCReader error:', err);
    this.errorCount++;

    if (this.errorCount >= MAX_ERRORS) {
      console.log(`连续错误 ${this.errorCount} 次，销毁实例`);
      this.destroyCurrentReader();
      appController.showMessage('连接失败，设备可能已离线', true);
    } else {
      appController.showMessage('已离线');
    }

    console.warn('playUrl:', playUrl);
  }

  // 处理连接成功
  handleConnectionSuccess(evt) {
    this.errorCount = 0;
    appController.showMessage('');
    video.srcObject = evt.streams[0];
  }

  // 销毁当前 Reader 实例
  destroyCurrentReader() {
    if (this.currentReader && typeof this.currentReader.close === 'function') {
      this.currentReader.close();
    }
    this.currentReader = null;
  }

  // 重新连接（刷新页面）
  reconnect() {
    setTimeout(() => {
      window.location.reload();
    }, 500);
  }
}

/**
 * 应用主控制器 - 协调各个组件
 */
class AppController {
  constructor() {
    this.buttonController = new ButtonController(reconnectBtn);
    this.connectionManager = new ConnectionManager();
    this.isManualMode = false;
    this.pendingConfig = null;
  }

  // 显示消息
  showMessage(text, showButton = false) {
    if (text !== '') {
      video.controls = false;
    } else {
      video.controls = defaultControls;
    }
    messageText.innerText = text;

    if (showButton) {
      this.buttonController.show();
    } else {
      this.buttonController.hide();
    }
  }

  // 处理手动连接
  async handleManualConnect() {
    if (!this.pendingConfig) return;

    this.buttonController.setLoadingState('初始化中...');

    try {
      await this.connectionManager.initializeConnection(this.pendingConfig);
      this.pendingConfig = null;
      this.isManualMode = false;

      // 切换到重试模式
      setTimeout(() => {
        this.buttonController.setRetryMode(() => this.handleReconnect());
      }, 1000);
    } catch (error) {
      console.error('Manual connect failed:', error);
      this.buttonController.resetState();
    }
  }

  // 处理重新连接
  handleReconnect() {
    this.buttonController.setLoadingState('重新连接中...');
    this.connectionManager.reconnect();
  }

  // 设置手动模式
  setManualMode(config) {
    this.isManualMode = true;
    this.pendingConfig = config;
    this.showMessage('等待连接', true);
    this.buttonController.setConnectMode(() => this.handleManualConnect());
  }

  // 设置自动模式
  async setAutoMode(config) {
    this.isManualMode = false;
    this.pendingConfig = null;

    try {
      await this.connectionManager.initializeConnection(config);
    } catch (error) {
      console.error('Auto connect failed:', error);
    }
  }
}

// 工具函数
const parseBoolString = (str, defaultVal) => {
  str = (str || '');

  if (['1', 'yes', 'true'].includes(str.toLowerCase())) {
    return true;
  }
  if (['0', 'no', 'false'].includes(str.toLowerCase())) {
    return false;
  }
  return defaultVal;
};

const loadAttributesFromQuery = () => {
  const params = new URLSearchParams(window.location.search);

  video.controls = parseBoolString(params.get('controls'), true);
  video.muted = parseBoolString(params.get('muted'), true);
  video.autoplay = parseBoolString(params.get('autoplay'), true);
  video.playsInline = parseBoolString(params.get('playsinline'), true);
  defaultControls = video.controls;

  return params;
};

// 创建应用控制器实例
const appController = new AppController();

// 监听消息事件
window.addEventListener('message', (event) => {
  loadAttributesFromQuery();

  const path = event.data.path || '';
  const headers = event.data.headers || {};
  const manual = event.data.manual;

  playUrl = `http://************:8889${path}/whep`;

  const config = { url: playUrl, headers };

  if (manual) {
    // 手动初始化模式
    appController.setManualMode(config);
  } else {
    // 自动初始化模式
    appController.setAutoMode(config);
  }
});
</script>

</body>
</html>
