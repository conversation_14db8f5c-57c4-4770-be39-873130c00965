export interface InspectionItem {
  id: number; // 主键id
  itemId: string; // 检修项ID
  itemNum: string; // 检修项编号
  itemName?: string; // 检修项名称
  componentType: string; // 部件类型（如发动机、液压系统）
  vehicleType: string; // 适用车型
  orderNum: number; // 环节顺序
  status: string; // 状态（启用 / 停用）
  standardProcess: string; // 标准流程说明
  pointCount: number; // 当前检修项的检修点总数
  itemImg: string; // 检查项图片
  remark?: string; // 备注
  createDept?: number; // 创建部门
  createBy?: number; // 创建者
  createUser: string; // 创建人
  createTime?: Date; // 创建时间
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
  detectionType?: string; // 检测项;
}
