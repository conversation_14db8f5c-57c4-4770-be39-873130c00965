import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions, getDict } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';

import {
  roleTypeDict,
  roleTypeMap,
  isKeyPointDict,
  isKeyPointMap,
  annexTypeDict,
  annexTypeMap,
} from './dict';

export const { renderDict, renderDictTags } = useRender();

export const columns: BasicColumn[] = [
  { title: '环节名称', dataIndex: 'stepName' },
  { title: '环节描述', dataIndex: 'description' },
  { title: '环节顺序', dataIndex: 'stepOrder' },
  {
    title: '角色类型',
    dataIndex: 'roleType',
    customRender({ value }) {
      return roleTypeMap[value] || value;
    },
  },
  {
    title: '附件类型',
    dataIndex: 'annexType',
    customRender({ value }) {
      return renderDict(value, DictEnum.STEP_ANNEX_TYPE);
    },
  },
  { title: '附件描述', dataIndex: 'annexDescription' },
  {
    title: '是否允许多个附件',
    dataIndex: 'isMultipleAttachments',
    customRender({ value }) {
      return renderDict(value, DictEnum.STEP_ANNEX_MULTIPLE_TYPE);
    },
  },
  {
    title: '识别项',
    dataIndex: 'detectionItem',
    customRender({ value }) {
      if (!value) return '';
      // 处理逗号分隔的多个值
      const values = value.split(',').filter((v) => v.trim());
      return renderDictTags(values, getDict(DictEnum.STEP_DETECTION_ITEM));
    },
  },
  { title: '前置环节', dataIndex: 'preStepNames' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '环节名称',
    field: 'stepName',
    component: 'Input',
  },
  {
    label: '环节描述',
    field: 'description',
    component: 'Input',
  },
  {
    label: '环节顺序',
    field: 'stepOrder',
    component: 'InputNumber',
  },
  {
    label: '角色类型',
    field: 'roleType',
    component: 'Select',
    componentProps: {
      options: roleTypeDict,
    },
  },
  {
    label: '附件类型',
    field: 'annexType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.STEP_ANNEX_TYPE),
    },
  },
  {
    label: '是否允许多个附件',
    field: 'isMultipleAttachments',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.STEP_ANNEX_MULTIPLE_TYPE),
    },
  },
  {
    label: '识别项',
    field: 'detectionItem',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.STEP_DETECTION_ITEM),
      mode: 'multiple',
    },
  },
  {
    label: '前置环节',
    field: 'preStepIds',
    component: 'Input',
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键 ID',
    component: 'Input',
    show: false,
  },
  {
    label: '环节名称',
    field: 'stepName',
    component: 'Input',
    required: true,
  },
  {
    label: '环节描述',
    field: 'description',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '环节顺序',
    field: 'stepOrder',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '角色类型',
    field: 'roleType',
    component: 'Select',
    componentProps: {
      options: roleTypeDict,
    },
    required: true,
  },
  {
    label: '附件类型',
    field: 'annexType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.STEP_ANNEX_TYPE),
    },
    required: true,
  },
  {
    label: '附件描述',
    field: 'annexDescription',
    component: 'InputTextArea',
  },
  {
    label: '是否允许多个附件',
    field: 'isMultipleAttachments',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.STEP_ANNEX_MULTIPLE_TYPE),
    },
    required: true,
  },
  {
    label: '识别项',
    field: 'detectionItem',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.STEP_DETECTION_ITEM),
      mode: 'multiple',
    },
  },
  {
    label: '前置环节',
    field: 'preStepIdList',
    slot: 'preStepIdList',
    defaultValue: [],
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];
