<template>
  <div class="face-recognition-content">
    <a-spin :spinning="loading">
      <div v-if="data.length > 0" class="face-list grid grid-cols-2 gap-4">
        <div v-for="item in data" :key="item.id" class="face-item">
          <div class="face-info">
            <div class="status">
              <Icon
                :icon="getStatusConfig(item.status).icon"
                :class="getStatusConfig(item.status).class"
              />
              <!-- <span>{{ getStatusConfig(item.status).text }}</span> -->
            </div>

            <div class="name flex-1 w-0">{{ item.userName }}</div>

            <div v-if="item.detectImagesUrl" class="image-action">
              <a-button type="link" size="small" @click="onPreviewImage(item.detectImagesUrl)">
                检测图片
              </a-button>
            </div>
            <div class="detect-time">{{ item.detectTime }}</div>
          </div>
        </div>
      </div>
      <a-empty v-else description="暂无数据" />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { Spin as ASpin, Empty as AEmpty, Button as AButton } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { FACE_STATUS_MAP } from '../constants';
  import type { FaceRecognitionData } from '../types';

  defineOptions({ name: 'FaceRecognitionTab' });

  interface Props {
    data: FaceRecognitionData[];
    loading: boolean;
  }

  interface Emits {
    (e: 'previewImage', imageUrl: string): void;
  }

  defineProps<Props>();
  const emit = defineEmits<Emits>();

  /**
   * 获取状态配置
   */
  function getStatusConfig(status: string) {
    return (
      FACE_STATUS_MAP[status as keyof typeof FACE_STATUS_MAP] || {
        text: '未知',
        icon: 'fa:question-circle',
        class: 'text-gray-500',
      }
    );
  }

  /**
   * 预览图片
   */
  function onPreviewImage(imageUrl: string) {
    emit('previewImage', imageUrl);
  }
</script>

<style scoped>
  .face-recognition-content {
    .face-item {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background: #fafafa;
    }

    .face-info {
      display: flex;
      align-items: center;

      .name {
        min-width: 80px;
        font-weight: 500;
      }

      .status {
        display: flex;
        align-items: center;
        gap: 4px;
        min-width: 30px;
      }

      .detect-time {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
</style>
