<template>
  <div class="utensil-content">
    <BasicTable
      :columns="tableColumns"
      :data-source="data"
      :loading="loading"
      :pagination="false"
      :show-table-setting="false"
      :use-search-form="false"
      :can-resize="false"
      size="small"
      :bordered="true"
      :action-column="{
        width: 200,
        title: '操作',
        key: 'action',
        fixed: 'right',
      }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <span :class="getStatusClass(record.status)">
            {{ getStatusText(record.status) }}
          </span>
        </template>
        <template v-else-if="column.key === 'action'">
          <TableAction stopButtonPropagation :actions="getTableActions(record)" align="center" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { BasicTable, TableAction, type BasicColumn, type ActionItem } from '@/components/Table';
  import { UTENSIL_STATUS_MAP } from '../constants';
  import type { UtensilRecognitionData, TableColumn } from '../types';

  defineOptions({ name: 'UtensilRecognitionTab' });

  interface Props {
    data: UtensilRecognitionData[];
    columns: TableColumn[];
    loading: boolean;
    isLowerUtensil?: boolean; // 是否为上下道工具清点
  }

  interface Emits {
    (e: 'previewImage', imageUrl: string): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  /**
   * 转换为BasicTable需要的列格式
   */
  const tableColumns = computed<BasicColumn[]>(() => {
    return props.columns.map((col) => ({
      title: col.title,
      dataIndex: col.dataIndex,
      key: col.key,
      width: col.width,
      align: col.align || 'center',
    }));
  });

  /**
   * 获取表格操作按钮
   */
  function getTableActions(record: UtensilRecognitionData): ActionItem[] {
    const actions: ActionItem[] = [];

    if (props.isLowerUtensil) {
      // 上下道工具清点
      if (record.upperTrackDetectImagesUrl) {
        actions.push({
          label: '上道图片',
          onClick: () => emit('previewImage', record.upperTrackDetectImagesUrl!),
        });
      }
      if (record.lowerTrackDetectImagesUrl) {
        actions.push({
          label: '下道图片',
          onClick: () => emit('previewImage', record.lowerTrackDetectImagesUrl!),
        });
      }
    } else {
      // 仅上道工具清点
      if (record.upperTrackDetectImagesUrl) {
        actions.push({
          label: '上道图片',
          onClick: () => emit('previewImage', record.upperTrackDetectImagesUrl!),
        });
      }
    }

    // 如果没有任何操作，返回一个占位符
    if (actions.length === 0) {
      actions.push({
        label: '-',
        disabled: true,
      });
    }

    return actions;
  }

  /**
   * 获取状态文本
   */
  function getStatusText(status?: string) {
    if (!status) return '-';
    return UTENSIL_STATUS_MAP[status as keyof typeof UTENSIL_STATUS_MAP] || status;
  }

  /**
   * 获取状态样式类
   */
  function getStatusClass(status?: string) {
    if (!status) return '';

    switch (status) {
      case '1':
        return 'text-green-600 font-medium'; // 一致 - 绿色
      case '2':
        return 'text-red-600 font-medium'; // 不一致 - 红色
      default:
        return 'text-gray-600';
    }
  }
</script>

<style scoped>
  .utensil-content {
    :deep(.ant-table-tbody > tr > td) {
      text-align: center;
    }

    :deep(.ant-table-thead > tr > th) {
      text-align: center;
    }
  }
</style>
