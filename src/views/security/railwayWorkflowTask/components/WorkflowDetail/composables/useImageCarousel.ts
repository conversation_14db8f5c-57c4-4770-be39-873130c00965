import { ref, computed } from 'vue';
import type { WorkflowStep } from '../types';

/**
 * 图片轮播管理 Composable
 */
export function useImageCarousel() {
  /**
   * 解析URL查询参数
   */
  function parseUrlParams(url: string): Record<string, string> {
    const params: Record<string, string> = {};
    try {
      const urlObj = new URL(url);
      urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
      });
    } catch (error) {
      // 如果URL解析失败，尝试从查询字符串中提取参数
      const queryIndex = url.indexOf('?');
      if (queryIndex !== -1) {
        const queryString = url.substring(queryIndex + 1);
        const pairs = queryString.split('&');
        pairs.forEach(pair => {
          const [key, value] = pair.split('=');
          if (key && value) {
            params[decodeURIComponent(key)] = decodeURIComponent(value);
          }
        });
      }
    }
    return params;
  }

  /**
   * 解析附件URL并按检测类型分组
   */
  function parseAnnexUrls(annexUrl?: string) {
    if (!annexUrl) return {};

    const urls = annexUrl.split(',').map(url => url.trim()).filter(url => url);
    const groupedImages: Record<string, string[]> = {};

    urls.forEach(url => {
      const params = parseUrlParams(url);
      const detectionType = params.detection || 'default';
      
      if (!groupedImages[detectionType]) {
        groupedImages[detectionType] = [];
      }
      groupedImages[detectionType].push(url);
    });

    return groupedImages;
  }

  /**
   * 根据当前tab获取对应的图片列表
   */
  function getImagesForTab(stepData: WorkflowStep | null, activeTabKey: string): string[] {
    if (!stepData?.annexUrl || !activeTabKey) return [];

    const groupedImages = parseAnnexUrls(stepData.annexUrl);
    
    // 优先匹配完全相同的key
    if (groupedImages[activeTabKey]) {
      return groupedImages[activeTabKey];
    }

    // 如果没有完全匹配，尝试匹配包含关系
    const matchingKey = Object.keys(groupedImages).find(key => 
      key.includes(activeTabKey) || activeTabKey.includes(key)
    );

    if (matchingKey) {
      return groupedImages[matchingKey];
    }

    // 如果都没有匹配，返回default分组或第一个分组
    if (groupedImages.default) {
      return groupedImages.default;
    }

    const firstKey = Object.keys(groupedImages)[0];
    return firstKey ? groupedImages[firstKey] : [];
  }

  /**
   * 检查URL是否为图片
   */
  function isImageUrl(url: string): boolean {
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp'];
    const lowerUrl = url.toLowerCase();
    return imageExtensions.some(ext => lowerUrl.includes(ext));
  }

  /**
   * 过滤出图片URL
   */
  function filterImageUrls(urls: string[]): string[] {
    return urls.filter(isImageUrl);
  }

  /**
   * 获取图片类型标签
   */
  function getImageTypeLabel(activeTabKey: string): string {
    const labelMap: Record<string, string> = {
      face: '人员清点',
      helmet_workwear: '着装检查',
      insulating_gloves_boots: '穿戴检查',
      grounding_wire: '接地靴',
      upper_utensil: '工具清点（上道）',
      lower_utensil: '工具清点（上下道）',
      voltage_detection: '验电检测',
      hang_grounding_pole: '地线检测',
    };
    
    return labelMap[activeTabKey] || activeTabKey;
  }

  return {
    parseAnnexUrls,
    getImagesForTab,
    isImageUrl,
    filterImageUrls,
    getImageTypeLabel,
  };
}
