<template>
  <BasicModal
    v-bind="$attrs"
    :width="1200"
    :title="`步骤详情：${stepData?.workflowName || ''}`"
    :footer="null"
    @register="registerInnerModal"
  >
    <div v-if="stepData" class="workflow-detail-modal !-mt-4 flex">
      <div class="flex-1 w-0">
        <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
          <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.label">
            <div class="tab-content">
              <!-- 人员清点 -->
              <FaceRecognitionTab
                v-if="isFaceRecognitionTab(tab.key)"
                :data="faceRecognitionData"
                :loading="loading"
                @preview-image="previewImage"
              />

              <!-- 着装检查、穿戴检查、接地线 -->
              <ManagementInfoTab
                v-else-if="isManagementInfoTab(tab.key)"
                :data="managementInfoData"
                :loading="loading"
                @preview-image="previewImage"
              />

              <!-- 工具清点 -->
              <UtensilRecognitionTab
                v-else-if="isUtensilRecognitionTab(tab.key)"
                :data="utensilRecognitionData"
                :columns="getUtensilColumns(tab.key)"
                :loading="loading"
                :is-lower-utensil="isLowerUtensilTab(tab.key)"
                @preview-image="previewImage"
              />
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 右侧图片轮播 -->
      <div class="flex-none w-80 ml-6">
        <div class="image-carousel-container">
          <!-- 标题 -->
          <div class="carousel-header">
            <h4 class="text-sm font-medium text-gray-700 mb-3"> {{ currentTabLabel }}图片 </h4>
          </div>

          <!-- 图片轮播 -->
          <div v-if="currentTabImages.length > 0" class="carousel-content">
            <a-carousel
              ref="carouselRef"
              :autoplay="false"
              effect="fade"
              dots
              :customPaging="customPagingFn"
            >
              <div v-for="(image, index) in currentTabImages" :key="index" class="carousel-slide">
                <div class="image-wrapper">
                  <Image
                    :src="image"
                    :alt="`${currentTabLabel}图片${index + 1}`"
                    class="carousel-image"
                    :preview="false"
                    @click="handleCarouselImageClick(image, index)"
                  />
                  <div class="image-overlay">
                    <span class="image-index">{{ index + 1 }} / {{ currentTabImages.length }}</span>
                  </div>
                </div>
              </div>
            </a-carousel>
          </div>

          <!-- 无图片时的占位 -->
          <div v-else class="no-images">
            <div class="no-images-content">
              <Icon icon="ant-design:picture-outlined" class="text-4xl text-gray-300 mb-2" />
              <p class="text-gray-400 text-sm">暂无相关图片</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <Image.PreviewGroup
      :preview="{
        visible: previewVisible,
        onVisibleChange: (visible) => (previewVisible = visible),
      }"
    >
      <Image v-for="(img, index) in previewImages" :key="index" :src="img" style="display: none" />
    </Image.PreviewGroup>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Tabs as ATabs, TabPane as ATabPane, Image, Carousel as ACarousel } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';

  // 导入子组件
  import FaceRecognitionTab from './WorkflowDetail/components/FaceRecognitionTab.vue';
  import ManagementInfoTab from './WorkflowDetail/components/ManagementInfoTab.vue';
  import UtensilRecognitionTab from './WorkflowDetail/components/UtensilRecognitionTab.vue';

  // 导入工具函数和常量
  import {
    parseDetectionItemToTabs,
    getFirstTabKey,
    isFaceRecognitionTab,
    isManagementInfoTab,
    isUtensilRecognitionTab,
    isLowerUtensilTab,
  } from './WorkflowDetail/utils/tabUtils';
  import { UPPER_UTENSIL_COLUMNS, LOWER_UTENSIL_COLUMNS } from './WorkflowDetail/constants';

  // 导入composables
  import { useWorkflowDetailData } from './WorkflowDetail/composables/useWorkflowDetailData';
  import { useImagePreview } from './WorkflowDetail/composables/useImagePreview';
  import { useImageCarousel } from './WorkflowDetail/composables/useImageCarousel';

  // 导入类型
  import type { WorkflowStep } from './WorkflowDetail/types';

  defineOptions({ name: 'WorkflowDetailModal' });

  const stepData = ref<WorkflowStep | null>(null);
  const activeTabKey = ref('');
  const carouselRef = ref();

  // 使用数据管理composable
  const {
    loading,
    faceRecognitionData,
    managementInfoData,
    utensilRecognitionData,
    loadTabData,
    clearAllData,
  } = useWorkflowDetailData();

  // 使用图片预览composable
  const { previewImages, previewVisible, previewImage } = useImagePreview();

  // 使用图片轮播composable
  const { getImagesForTab, filterImageUrls, getImageTypeLabel } = useImageCarousel();

  // 计算动态tabs
  const tabList = computed(() => parseDetectionItemToTabs(stepData.value));

  // 计算当前tab对应的图片列表
  const currentTabImages = computed(() => {
    const allImages = getImagesForTab(stepData.value, activeTabKey.value);
    return filterImageUrls(allImages);
  });

  // 计算当前tab的标签
  const currentTabLabel = computed(() => getImageTypeLabel(activeTabKey.value));

  // 获取工具清点表格列
  function getUtensilColumns(tabKey: string) {
    return isLowerUtensilTab(tabKey) ? LOWER_UTENSIL_COLUMNS : UPPER_UTENSIL_COLUMNS;
  }

  const [registerInnerModal] = useModalInner(async (step: WorkflowStep) => {
    if (!step) return;

    console.log('stepData.value', step);

    stepData.value = step;
    clearAllData();

    // 如果有detectionItem，设置第一个tab为活跃状态
    const firstTabKey = getFirstTabKey(step);
    if (firstTabKey) {
      activeTabKey.value = firstTabKey;
      await loadTabData(firstTabKey, step);
    }
  });

  // 切换tab时加载数据
  async function handleTabChange(key: any) {
    activeTabKey.value = key;
    if (stepData.value) {
      await loadTabData(key, stepData.value);
    }
  }

  // 处理轮播图片点击
  function handleCarouselImageClick(_clickedImage: string, _clickedIndex: number) {
    // 设置预览图片列表为当前tab的所有图片
    previewImages.value = currentTabImages.value;
    // 设置预览可见
    previewVisible.value = true;
  }

  // 自定义分页器函数
  function customPagingFn(i: number) {
    return `<div class="thumbnail-paging">
      <img src="${currentTabImages.value[i]}" alt="缩略图${i + 1}" class="thumbnail-image" />
    </div>`;
  }
</script>

<style scoped>
  .workflow-detail-modal {
    min-height: 400px;
  }

  /* 图片轮播容器样式 */
  .image-carousel-container {
    display: flex;
    flex-direction: column;
    height: 450px;
    padding: 16px;
    border-radius: 8px;
    background: #fafafa;
  }

  .carousel-header {
    flex-shrink: 0;
  }

  .carousel-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding-bottom: 60px; /* 为缩略图分页器留出空间 */
  }

  .carousel-slide {
    position: relative;
    height: 300px;
  }

  .image-wrapper {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    height: 100%;
    overflow: hidden;
    border-radius: 6px;
    background-color: #000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
  }

  .carousel-image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
    cursor: pointer;
    object-fit: cover;
  }

  .carousel-image:hover {
    transform: scale(1.02);
  }

  .image-overlay {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 12px 16px 8px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 60%));
    color: white;
  }

  .image-index {
    font-size: 12px;
    font-weight: 500;
  }

  /* 无图片占位样式 */
  .no-images {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    border: 2px dashed #e0e0e0;
    border-radius: 6px;
    background: #f8f9fa;
  }

  .no-images-content {
    text-align: center;
  }

  /* 缩略图分页器样式 */
  .thumbnail-paging {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 35px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    border-radius: 4px;
    cursor: pointer;
  }

  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    border-radius: 2px;
  }

  /* 轮播指示器样式 */
  :deep(.ant-carousel .slick-dots) {
    display: flex !important;
    bottom: -50px;
    justify-content: center;
    padding: 0 16px;
    gap: 8px;
  }

  :deep(.ant-carousel .slick-dots li) {
    width: auto;
    height: auto;
    margin: 0;
  }

  :deep(.ant-carousel .slick-dots li button) {
    width: 50px;
    height: 35px;
    padding: 0;
    transition: all 0.3s ease;
    border: none;
    border-radius: 4px;
    opacity: 0.7;
    background: transparent;
  }

  :deep(.ant-carousel .slick-dots li button:hover) {
    transform: scale(1.05);
    opacity: 0.9;
  }

  :deep(.ant-carousel .slick-dots li.slick-active button) {
    border: 2px solid #1890ff;
    opacity: 1;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 30%);
  }

  /* 轮播容器样式 */
  :deep(.ant-carousel) {
    position: relative;
    flex: 1;
  }

  :deep(.ant-carousel .slick-slide) {
    height: 300px;
  }

  :deep(.ant-carousel .slick-slide > div) {
    height: 100%;
  }

  :deep(.ant-carousel .slick-list) {
    height: 300px;
  }
</style>
