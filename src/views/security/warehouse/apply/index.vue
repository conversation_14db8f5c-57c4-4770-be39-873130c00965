<template>
  <div class="warehouse-apply-form size-screen overflow-auto">
    <a-card class="mb-4">
      <template #title>
        <div class="w-full text-center font-bold text-primary-500">料库工单申请</div>
      </template>

      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        class="-mt-4"
        @finish="handleSubmit"
      >
        <!-- 基本信息区块 -->
        <div
          class="bg-primary-50 !text-primary-500 rounded-md overflow-hidden px-4 py-2 mt-4 mb-8 font-bold"
          >基本信息</div
        >

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="工作区域" name="workArea">
              <a-input v-model:value="formData.workArea" placeholder="请输入工作区域" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工票号" name="workTicketNum">
              <a-input v-model:value="formData.workTicketNum" placeholder="请输入工票号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="施工日计划号" name="constructionDayPlanNum">
              <a-input
                v-model:value="formData.constructionDayPlanNum"
                placeholder="请输入施工日计划号"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工票有效期" name="workTicketValidityPeriod">
              <a-range-picker
                v-model:value="formData.workTicketValidityPeriod"
                show-time
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 人员信息区块 -->
        <div
          class="bg-primary-50 !text-primary-500 rounded-md overflow-hidden px-4 py-2 mt-4 mb-8 font-bold"
          >人员信息</div
        >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="工作负责人" name="workLeaderId">
              <WarehouseWorkerSelect
                v-model:value="formData.workLeaderId"
                placeholder="请选择工作负责人"
                @change="(value, worker) => handleWorkerChange('workLeader', value, worker)"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="车站联络人" name="stationLiaisonPersonId">
              <WarehouseWorkerSelect
                v-model:value="formData.stationLiaisonPersonId"
                placeholder="请选择车站联络人"
                @change="
                  (value, worker) => handleWorkerChange('stationLiaisonPerson', value, worker)
                "
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="地线监护人" name="groundLineGuardianId">
              <WarehouseWorkerSelect
                v-model:value="formData.groundLineGuardianId"
                mode="multiple"
                placeholder="请选择地线监护人（可多选）"
                @change="
                  (value, workers) =>
                    handleMultipleWorkerChange('groundLineGuardian', value, workers)
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工作组员" name="workTeamIds">
              <WarehouseWorkerSelect
                v-model:value="formData.workTeamIds"
                mode="multiple"
                placeholder="请选择工作组员（可多选）"
                @change="(value, workers) => handleMultipleWorkerChange('workTeam', value, workers)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="责任人" name="responsiblePersonId">
              <WarehouseWorkerSelect
                v-model:value="formData.responsiblePersonId"
                placeholder="请选择责任人"
                @change="(value, worker) => handleWorkerChange('responsiblePerson', value, worker)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 站点和料库信息区块 -->
        <div
          class="bg-primary-50 !text-primary-500 rounded-md overflow-hidden px-4 py-2 mt-4 mb-8 font-bold"
          >站点和料库信息</div
        >

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="站点名称" name="stationName">
              <a-input v-model:value="formData.stationName" placeholder="请输入站点名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="料库名称" name="warehouseName">
              <a-input v-model:value="formData.warehouseName" placeholder="请输入料库名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 任务信息区块 -->
        <div
          class="bg-primary-50 !text-primary-500 rounded-md overflow-hidden px-4 py-2 mt-4 mb-8 font-bold"
          >任务信息</div
        >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="工单类型" name="workOrderType">
              <a-input v-model:value="formData.workOrderType" placeholder="请输入工单类型" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="任务类型" name="taskType">
              <a-input v-model:value="formData.taskType" placeholder="请输入任务类型" />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="utensils-section pt-4 mt-4 border-t border-primary-50">
          <a-button type="primary" @click="addUtensil" class="ml-auto mb-4" :icon="h(PlusOutlined)">
            添加工器具
          </a-button>

          <a-table
            :columns="utensilColumns"
            :data-source="formData.taskUtensils"
            :pagination="false"
            size="small"
            bordered
            class="!w-full"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'utensilName'">
                <a-input
                  v-model:value="record.utensilName"
                  placeholder="请输入工器具名称"
                  @change="validateUtensil(index)"
                />
              </template>
              <template v-else-if="column.key === 'utensilType'">
                <a-input
                  v-model:value="record.utensilType"
                  placeholder="请输入工器具类型"
                  @change="validateUtensil(index)"
                />
              </template>
              <template v-else-if="column.key === 'utensilCount'">
                <a-input-number
                  v-model:value="record.utensilCount"
                  :min="1"
                  placeholder="数量"
                  style="width: 100%"
                  @change="validateUtensil(index)"
                />
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button
                  type="link"
                  danger
                  @click="removeUtensil(index)"
                  :icon="h(DeleteOutlined)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 表单操作按钮 -->
        <div class="mt-8">
          <a-form-item :wrapper-col="{ offset: 10, span: 14 }">
            <a-space>
              <a-button type="primary" size="large" html-type="submit" :loading="submitLoading">
                提交申请
              </a-button>
              <a-button size="large" @click="resetForm">重置</a-button>
            </a-space>
          </a-form-item>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, h } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import WarehouseWorkerSelect from './components/WarehouseWorkerSelect.vue';
  import {
    saveMaterialWarehouseWork,
    type MaterialWarehouseWorkForm,
    type WorkerInfo,
  } from './api';

  // 表单引用
  const formRef = ref();
  const submitLoading = ref(false);

  // 表单数据
  const formData = reactive<
    MaterialWarehouseWorkForm & {
      workLeaderId?: string;
      stationLiaisonPersonId?: string;
      groundLineGuardianId?: string[];
      workTeamIds?: string[];
      responsiblePersonId?: string;
    }
  >({
    workArea: void 0,
    workTicketNum: void 0,
    constructionDayPlanNum: void 0,
    workTicketValidityPeriod: [],
    workLeader: void 0,
    workLeaderId: void 0,
    stationLiaisonPerson: void 0,
    stationLiaisonPersonId: void 0,
    groundLineGuardian: void 0,
    groundLineGuardianId: [],
    workTeam: void 0,
    workTeamIds: [],
    stationId: void 0,
    stationName: void 0,
    warehouseId: void 0,
    warehouseName: void 0,
    workOrderType: void 0,
    taskType: void 0,
    responsiblePerson: void 0,
    responsiblePersonId: void 0,
    taskUtensils: [],
  });

  // 表单验证规则
  const formRules = {
    workArea: [{ required: true, message: '请输入工作区域', trigger: 'blur' }],
    workTicketNum: [{ required: true, message: '请输入工票号', trigger: 'blur' }],
    constructionDayPlanNum: [{ required: true, message: '请输入施工日计划号', trigger: 'blur' }],
    workTicketValidityPeriod: [{ required: true, message: '请选择工票有效期', trigger: 'change' }],
    workLeaderId: [{ required: true, message: '请选择工作负责人', trigger: 'change' }],
    stationLiaisonPersonId: [{ required: true, message: '请选择车站联络人', trigger: 'change' }],
    groundLineGuardianId: [{ required: true, message: '请选择地线监护人', trigger: 'change' }],
    workTeamIds: [{ required: true, message: '请选择工作组员', trigger: 'change' }],
    stationId: [{ required: true, message: '请输入站点ID', trigger: 'blur' }],
    stationName: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
    warehouseId: [{ required: true, message: '请输入料库ID', trigger: 'blur' }],
    warehouseName: [{ required: true, message: '请输入料库名称', trigger: 'blur' }],
    workOrderType: [{ required: true, message: '请输入工单类型', trigger: 'blur' }],
    taskType: [{ required: true, message: '请输入任务类型', trigger: 'blur' }],
    responsiblePersonId: [{ required: true, message: '请选择责任人', trigger: 'change' }],
  };

  // 工器具表格列定义
  const utensilColumns = [
    {
      title: '工器具名称',
      key: 'utensilName',
      dataIndex: 'utensilName',
      width: '35%',
    },
    {
      title: '工器具类型',
      key: 'utensilType',
      dataIndex: 'utensilType',
      width: '35%',
    },
    {
      title: '数量',
      key: 'utensilCount',
      dataIndex: 'utensilCount',
      width: '20%',
    },
    {
      title: '操作',
      key: 'action',
      width: '10%',
    },
  ];

  // 处理单个人员选择变化
  function handleWorkerChange(
    field: string,
    _value: string | number | string[] | number[] | undefined,
    worker: WorkerInfo | WorkerInfo[] | null,
  ) {
    // 确保是单个 WorkerInfo 对象
    if (worker && !Array.isArray(worker)) {
      formData[field] = worker.userName;
    } else {
      formData[field] = '';
    }
  }

  // 处理多个人员选择变化
  function handleMultipleWorkerChange(
    field: string,
    _value: string | number | string[] | number[] | undefined,
    workers: WorkerInfo | WorkerInfo[] | null,
  ) {
    // 确保是 WorkerInfo 数组
    if (workers && Array.isArray(workers) && workers.length > 0) {
      const names = workers.map((worker) => worker.userName).join(',');
      formData[field] = names;
    } else {
      formData[field] = '';
    }
  }

  // 添加工器具
  function addUtensil() {
    formData.taskUtensils.push({
      utensilName: '',
      utensilType: '',
      utensilCount: 1,
    });
  }

  // 删除工器具
  function removeUtensil(index: number) {
    if (formData.taskUtensils.length > 1) {
      formData.taskUtensils.splice(index, 1);
    } else {
      message.warning('至少保留一个工器具项');
    }
  }

  // 验证工器具
  function validateUtensil(index: number) {
    const utensil = formData.taskUtensils[index];
    if (!utensil.utensilName || !utensil.utensilType || !utensil.utensilCount) {
      // 可以添加更详细的验证逻辑
    }
  }

  // 表单提交
  async function handleSubmit() {
    try {
      await formRef.value.validate();

      // 验证工器具信息
      const hasEmptyUtensil = formData.taskUtensils.some(
        (utensil) => !utensil.utensilName || !utensil.utensilType || !utensil.utensilCount,
      );

      if (hasEmptyUtensil) {
        message.error('请完善工器具信息');
        return;
      }

      submitLoading.value = true;

      // 准备提交数据
      const submitData: MaterialWarehouseWorkForm = {
        workArea: formData.workArea,
        workTicketNum: formData.workTicketNum,
        constructionDayPlanNum: formData.constructionDayPlanNum,
        workTicketValidityPeriod: `${formData.workTicketValidityPeriod[0]} 至 ${formData.workTicketValidityPeriod[1]}`,
        workLeader: formData.workLeader,
        workLeaderId: formData.workLeaderId,
        stationLiaisonPerson: formData.stationLiaisonPerson,
        stationLiaisonPersonId: formData.stationLiaisonPersonId,
        groundLineGuardian: formData.groundLineGuardian,
        groundLineGuardianId: Array.isArray(formData.groundLineGuardianId)
          ? formData.groundLineGuardianId.join(',')
          : formData.groundLineGuardianId,
        workTeam: formData.workTeam,
        stationId: formData.stationId,
        stationName: formData.stationName,
        warehouseId: formData.warehouseId,
        warehouseName: formData.warehouseName,
        workOrderType: formData.workOrderType,
        taskType: formData.taskType,
        responsiblePerson: formData.responsiblePerson,
        responsiblePersonId: formData.responsiblePersonId,
        taskUtensils: formData.taskUtensils,
      };

      const response = await saveMaterialWarehouseWork(submitData);

      if (response.code === 200) {
        message.success('工单申请提交成功！');
        resetForm();
      } else {
        message.error(response.msg || '提交失败，请重试');
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('提交失败，请检查表单信息');
    } finally {
      submitLoading.value = false;
    }
  }

  // 重置表单
  function resetForm() {
    formRef.value.resetFields();
    Object.assign(formData, {
      workArea: '',
      workTicketNum: '',
      constructionDayPlanNum: '',
      workTicketValidityPeriod: [],
      workLeader: '',
      workLeaderId: '',
      stationLiaisonPerson: '',
      stationLiaisonPersonId: '',
      groundLineGuardian: '',
      groundLineGuardianId: [],
      workTeam: '',
      workTeamIds: [],
      stationId: '',
      stationName: '',
      warehouseId: '',
      warehouseName: '',
      workOrderType: '',
      taskType: '',
      responsiblePerson: '',
      responsiblePersonId: '',
      taskUtensils: [{ utensilName: '', utensilType: '', utensilCount: 1 }],
    });
  }
</script>

<style scoped>
  .warehouse-apply-form {
    padding: 24px;
    background-color: #f5f5f5;
  }

  .utensils-section {
    width: 100%;
  }

  :deep(.ant-divider-horizontal.ant-divider-with-text-left) {
    margin: 24px 0 16px;
  }

  :deep(.ant-divider-horizontal.ant-divider-with-text-left::before) {
    width: 5%;
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }

  :deep(.ant-card-head-title) {
    font-size: 18px;
    font-weight: 600;
  }
</style>
