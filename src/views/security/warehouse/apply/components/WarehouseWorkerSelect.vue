<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :allowClear="allowClear"
    :disabled="disabled"
    :mode="mode"
    show-search
    :filter-option="false"
    :not-found-content="loading ? undefined : null"
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear"
    @focus="onFocus"
    :style="{ width: '100%' }"
  >
    <template #notFoundContent>
      <a-spin v-if="loading" size="small" />
      <span v-else>暂无数据</span>
    </template>
    <a-select-option
      v-for="worker in workerOptions"
      :key="worker.id"
      :value="worker.id"
      :label="worker.userName"
    >
      <div class="flex items-center h-full">
        <div class="font-medium flex-none min-w-14">{{ worker.userName }}</div>

        <div v-if="worker.workerNum" class="text-xs text-gray-500 flex-none ml-4"
          >工号: {{ worker.workerNum }}</div
        >

        <div v-if="worker.stationName" class="text-xs text-gray-500 flex-none ml-4"
          >站点: {{ worker.stationName }}</div
        >
      </div>
    </a-select-option>
  </a-select>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, computed } from 'vue';
  import { Select as ASelect, SelectOption as ASelectOption, Spin as ASpin } from 'ant-design-vue';
  import { getWorkerList, type WorkerInfo } from '../api';
  import { useDebounceFn } from '@vueuse/core';

  interface Props {
    value?: string | number | string[] | number[];
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
    mode?: 'multiple' | 'tags' | undefined;
    stationId?: string | number;
  }

  interface Emits {
    (e: 'update:value', value: string | number | string[] | number[] | undefined): void;
    (
      e: 'change',
      value: string | number | string[] | number[] | undefined,
      workers: WorkerInfo | WorkerInfo[] | null,
    ): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择人员',
    allowClear: true,
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  const loading = ref(false);
  const workerOptions = ref<WorkerInfo[]>([]);
  const searchKeyword = ref('');

  // 双向绑定的值
  const selectedValue = computed({
    get: () => props.value,
    set: (val) => emit('update:value', val),
  });

  // 防抖搜索
  const debouncedSearch = useDebounceFn((keyword: string) => {
    fetchWorkers(keyword);
  }, 300);

  // 获取人员列表
  async function fetchWorkers(keyword = '') {
    try {
      loading.value = true;
      const response = await getWorkerList();

      let workers = response || [];

      // 根据关键词过滤
      if (keyword) {
        workers = workers.filter(
          (worker) => worker.userName.includes(keyword) || worker.workerNum.includes(keyword),
        );
      }

      // 根据站点过滤
      if (props.stationId) {
        workers = workers.filter((worker) => worker.stationId === String(props.stationId));
      }

      workerOptions.value = workers;
    } catch (error) {
      console.error('获取人员列表失败:', error);
      workerOptions.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 搜索处理
  function handleSearch(value: string) {
    searchKeyword.value = value;
    debouncedSearch(value);
  }

  // 值变化处理
  function handleChange(value: string | number | string[] | number[] | undefined) {
    emit('update:value', value);

    // 找到对应的 worker 对象
    if (value) {
      if (props.mode === 'multiple') {
        const selectedWorkers = workerOptions.value.filter(
          (worker) => Array.isArray(value) && value.includes(worker.id),
        );
        emit('change', value, selectedWorkers);
      } else {
        const selectedWorker = workerOptions.value.find((worker) => worker.id === value);
        emit('change', value, selectedWorker || null);
      }
    } else {
      emit('change', value, null);
    }
  }

  // 清空处理
  function handleClear() {
    emit('update:value', undefined);
    emit('change', undefined, null);
  }

  // 获得焦点时加载数据
  function onFocus() {
    if (workerOptions.value.length === 0) {
      fetchWorkers();
    }
  }

  // 监听站点变化
  watch(
    () => props.stationId,
    () => {
      fetchWorkers(searchKeyword.value);
    },
  );

  // 组件挂载时加载数据
  onMounted(() => {
    fetchWorkers();
  });
</script>

<style scoped>
  .worker-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .worker-name {
    font-weight: 500;
  }

  .worker-info {
    display: flex;
    color: #666;
    font-size: 12px;
    gap: 8px;
  }

  .worker-num {
    color: #999;
  }

  .worker-station {
    color: #1890ff;
  }
</style>
