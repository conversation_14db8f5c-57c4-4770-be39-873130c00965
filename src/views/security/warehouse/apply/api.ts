import { defHttp } from '@/utils/http/axios';

// 人员信息接口类型定义
export interface WorkerInfo {
  id: string | number;
  workerNum?: string;
  userName?: string;
  gender?: string;
  type?: string;
  image?: string;
  status?: string;
  imageUrl?: string;
  qualificationInformation?: string | null;
  stationId?: string;
  stationName?: string | null;
  phone?: string;
  violationCount?: number | null;
  remark?: string | null;
  broadcastStatus?: number | null;
  promptStatus?: number | null;
}

// 工器具信息类型定义
export interface TaskUtensil {
  utensilName?: string;
  utensilType?: string;
  utensilCount?: number;
}

// 表单提交数据类型定义
export interface MaterialWarehouseWorkForm {
  userId?: string;
  userName?: string;
  workArea?: string;
  workTicketNum?: string;
  constructionDayPlanNum?: string;
  workTicketValidityPeriod: string[] | string;
  workLeader?: string;
  workLeaderId?: string;
  stationLiaisonPerson?: string;
  stationLiaisonPersonId?: string;
  groundLineGuardian?: string;
  groundLineGuardianId?: string[] | string;
  workTeam?: string;
  stationId?: string;
  stationName?: string;
  warehouseId?: string;
  warehouseName?: string;
  workOrderType?: string;
  taskType?: string;
  responsiblePerson?: string;
  responsiblePersonId?: string;
  taskUtensils: TaskUtensil[];
}

// API 响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  token: null;
  uri: null;
  data: T;
  request_time: null;
  response_time: null;
  cost_time: null;
  debug_image_url: null;
}

/**
 * 获取人员下拉列表信息
 */
export function getWorkerList() {
  return defHttp.get<ApiResponse<WorkerInfo[]>>({
    url: '/business/materialWarehouseWork/getWorkerList',
  });
}

/**
 * 保存料库工单表单信息
 */
export function saveMaterialWarehouseWork(data: MaterialWarehouseWorkForm) {
  return defHttp.post<ApiResponse>({
    url: '/business/materialWarehouseWork/save',
    data,
  });
}
