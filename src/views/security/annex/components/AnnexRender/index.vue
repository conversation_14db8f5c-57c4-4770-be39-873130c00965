<template>
  <div class="flex items-center">
    <Image v-if="checkImage" :src="value" class="!h-12 min-w-12" />

    <div v-else-if="checkVideo" class="relative cursor-pointer" @click="onVideoClick">
      <video ref="videoRef" :src="value" class="!h-12 !min-w-12"></video>
      <div
        class="size-6 i-carbon-play-outline-filled absolute left-1/2 top-1/2 bg-gray-50 -translate-y-1/2 -translate-x-1/2"
      ></div>
    </div>

    <div
      v-else-if="checkAudio"
      class="relative cursor-pointer bg-gray-500 !h-12 !w-22 overflow-auto rounded"
      @click="onAudioClick"
    >
      <div
        class="size-6 i-carbon-volume-up absolute left-1/2 top-1/2 bg-gray-50 -translate-y-1/2 -translate-x-1/2"
      ></div>
    </div>
  </div>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { Image } from 'ant-design-vue';

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
  });

  const checkImage = computed(() =>
    ['.png', '.jpg', '.jpeg'].some((item) => props.value.includes(item)),
  );

  const checkVideo = computed(() => ['.mp4'].some((item) => props.value.includes(item)));

  const checkAudio = computed(() => ['.mp3'].some((item) => props.value.includes(item)));

  const videoRef = ref();

  function onVideoClick() {
    window.open(props.value);
  }

  function onAudioClick() {
    window.open(props.value);
  }
</script>
<style lang=""></style>
