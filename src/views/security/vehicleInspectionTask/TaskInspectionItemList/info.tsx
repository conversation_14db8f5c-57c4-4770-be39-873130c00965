import { DescItem } from '@/components/Description';
import AnnexRender from '@/views/security/annex/components/AnnexRender/index.vue';
import { DictEnum } from '@/enums/dictEnum';
import { useRender } from '@/hooks/component/useRender';

export const { renderDict } = useRender();

export const descSchema: DescItem[] = [
  {
    label: '检查项图片',
    field: 'itemImg',
    render: (val) => <AnnexRender value={val} />,
  },
  {
    label: '数字编号',
    field: 'number',
  },
  {
    label: '检修项编号',
    field: 'itemNum',
  },
  {
    label: '检修项名称',
    field: 'itemName',
  },
  {
    label: '是否完成',
    field: 'isCompleted',
    render: (value) => {
      return renderDict(value, DictEnum.VEHICLE_ITEM_COMPLETED);
    },
  },
  {
    label: '检测记录图片',
    field: 'detectImg',
    render: (val) => <AnnexRender urls={val} />,
  },
  {
    label: '检测类别',
    field: 'types',
  },
  {
    label: '回调标记',
    field: 'sends',
  },
  {
    label: '标准流程说明',
    field: 'standardProcess',
  },
  {
    label: '排序',
    field: 'orderNum',
  },
  {
    label: '检修点总数',
    field: 'pointTotal',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
  {
    label: '更新时间',
    field: 'updateTime',
  },
];
