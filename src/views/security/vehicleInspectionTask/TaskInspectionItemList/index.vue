<template>
  <div class="">
    <BasicTable @register="registerTable">
      <!-- <template #toolbar>
        <Space>
          <a-button
            class="<sm:hidden"
            @click="downloadExcel(itemRecordExport, '任务检查项管理', getForm().getFieldsValue())"
            >导出</a-button
          >
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(itemRecordRemove)"
            :disabled="!selected"
            >删除</a-button
          >
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </Space>
      </template> -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              // {
              //   label: '修改',
              //   icon: IconEnum.EDIT,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleEdit.bind(null, record),
              // },
              // {
              //   label: '删除',
              //   icon: IconEnum.DELETE,
              //   type: 'primary',
              //   danger: true,
              //   ghost: true,
              //   popConfirm: {
              //     placement: 'left',
              //     title: `是否确认删除?`,
              //     confirm: handleDelete.bind(null, record),
              //   },
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ItemRecordModal @register="registerModal" @reload="reload" />
    <ItemRecordInfoModal @register="registerInfoModal" />
  </div>
</template>

<script setup lang="ts">
  // import { PageWrapper } from '@/components/Page';
  // import { Space } from 'ant-design-vue';
  // import { downloadExcel } from '@/utils/file/download';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { itemRecordList, itemRecordRemove } from '@/api/security/vehicleInspectionTaskItem';
  import ItemRecordModal from './Modal.vue';
  import { useModal } from '@/components/Modal';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';
  import ItemRecordInfoModal from './InfoModal.vue';

  defineOptions({ name: 'TaskInspectionItemList' });

  const props = defineProps({
    params: {
      type: Object,
      default: () => ({}),
    },
  });

  const [registerTable, { reload, multipleRemove, selected, getForm }] = useTable({
    canResize: false,
    showTableSetting: false,
    rowSelection: {
      type: 'checkbox',
    },
    // title: '任务检查项管理',
    showIndexColumn: false,
    api: (params) =>
      itemRecordList({
        ...props.params,
        ...params,
      }),
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'itemRecord',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: columns,
    actionColumn: {
      width: 120,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  async function handleDelete(record: Recordable) {
    const { id: itemRecordId } = record;
    await itemRecordRemove([itemRecordId]);
    await reload();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    openInfoModal(true, record);
  }
</script>

<style scoped></style>
