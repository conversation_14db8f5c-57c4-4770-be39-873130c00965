<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    @cancel="resetForm"
  >
    <BasicForm @register="registerForm">
      <template #stationId="{ model, field }">
        <StationSelect v-model:value="model[field]" v-model:label="model.stationName" />
      </template>
      <template #image="{ model, field }">
        <ImageUpload
          v-model:value="model[field]"
          :maxNumber="1"
          :maxSize="5"
          :accept="['jpg', 'jpeg', 'png', 'gif', 'webp']"
          :api="uploadApi"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { computed, ref, unref } from 'vue';
  import { workerInfoUpdate, workerInfoAdd, workerInfoDetail } from '@/api/security/worker';
  // import { stationOptionSelect } from '@/api/business/station';
  import { modalSchemas } from './data';
  import StationSelect from '@/views/security/components/StationSelect/index.vue';
  import { ImageUpload } from '@/components/Upload';
  import { uploadApi } from '@/api/upload';

  defineOptions({ name: 'WorkerInfo' });

  const emit = defineEmits(['register', 'reload']);

  const isUpdate = ref<boolean>(false);

  const title = computed<string>(() => {
    return isUpdate.value ? '编辑人员信息' : '新增人员信息';
  });

  const retData = ref<any>({});

  const [registerInnerModal, { modalLoading, closeModal }] = useModalInner(
    async (data: { record?: Recordable; update: boolean }) => {
      modalLoading(true);
      const { record, update } = data;
      isUpdate.value = update;
      if (update && record) {
        const ret = await workerInfoDetail(record.id);
        retData.value = ret;

        await setFieldsValue(ret);
      }
      modalLoading(false);
    },
  );

  const [registerForm, { setFieldsValue, resetForm, validate, updateSchema }] = useForm({
    baseColProps: {
      span: 24,
    },
    labelWidth: 100,
    name: 'workerInfo_modal',
    showActionButtonGroup: false,
    schemas: modalSchemas,
  });

  async function handleSubmit() {
    try {
      modalLoading(true);
      const data = await validate();

      if (Array.isArray(data.image)) {
        data.image = data.image.join(',');
      }

      if (unref(isUpdate)) {
        await workerInfoUpdate(data);
      } else {
        await workerInfoAdd(data);
      }
      emit('reload');
      closeModal();
      await resetForm();
    } catch (e) {
      console.log(e);
    } finally {
      modalLoading(false);
    }
  }
</script>

<style scoped></style>
